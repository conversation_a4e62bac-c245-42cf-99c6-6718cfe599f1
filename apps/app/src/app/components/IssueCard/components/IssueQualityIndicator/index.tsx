import { useMessageGetter } from "@messageformat/react";
import type { IssueDetailsBasicSchema } from "@shape-construction/api/src/types";
import Popover from "@shape-construction/arch-ui/src/Popover";
import { ProgressBarRoot } from "@shape-construction/arch-ui/src/ProgressBar/ProgressBar";
import { breakpoints } from "@shape-construction/arch-ui/src/utils/breakpoints";
import { cn } from "@shape-construction/arch-ui/src/utils/classes";
import { useMediaQuery } from "@shape-construction/hooks";
import { useState } from "react";

type IssueQualityIndicatorProps = {
    className?: string;
    qualityScore: IssueDetailsBasicSchema['qualityScore'];
    title: string;
    scoreRange: string;
    description: string;
};

function getProgressBarColor(qualityScore: number) {
    if (qualityScore < 19) return 'danger';
    if (qualityScore >= 19 || qualityScore < 30) return 'warning';
}

export const IssueQualityIndicator: React.FC<IssueQualityIndicatorProps> = ({ className, qualityScore }) => {
    const [issueQualityPopoverOpen, setIssueQualityPopoverOpen] = useState(false);
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));

    if (qualityScore === null || qualityScore > 30) {
        return <div className={className} />
    };

    return (
        <div className={cn(className, 'flex justify-center items-center rounded-full w-auto p-0.5 hover:bg-neutral-subtle')}
            onPointerEnter={() => isLargeScreen && setIssueQualityPopoverOpen(true)}
            onPointerLeave={() => isLargeScreen && setIssueQualityPopoverOpen(false)}
        >
            <Popover open={issueQualityPopoverOpen} onOpenChange={setIssueQualityPopoverOpen}>
                <Popover.Trigger asChild>
                    <div
                        className="cursor-pointer"
                        onClick={(e) => {
                            e.preventDefault();
                            setIssueQualityPopoverOpen(true);
                        }}
                    >
                        <ProgressBarRoot progress={qualityScore} size="small" color={getProgressBarColor(qualityScore)} variant='donut' />
                    </div>
                </Popover.Trigger>
                <Popover.Content side="bottom">
                    <div className="flex flex-col gap-1 text-sm leading-6 font-medium">
                        <span>Not useful</span>
                        <span className="font-bold">Score range: 23</span>
                        <span className="font-normal">
                            Issues are severely lacking in essential information or have been inactive for too long.
                        </span>
                    </div>
                </Popover.Content>
            </Popover>
        </div>
    );
};
